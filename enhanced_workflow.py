#!/usr/bin/env python3
"""
Enhanced Target Exploit Workflow with Real API Integrations
"""

import json
import asyncio
import aiohttp
from urllib.parse import urlparse
from typing import List, Dict, Any, Optional
import logging
import csv
import os
from datetime import datetime
from config import WorkflowConfig, PROMPTS, TEMPLATES
from target_exploit_workflow import WorkflowInput, EntityResult, BaseNode, SubdomainInfo, resolve_subdomains_ips

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# SERP API will be imported when needed


class EnhancedWikipediaSearchNode(BaseNode):
    """Enhanced Wikipedia search using the wikipedia package - returns all search results"""

    def __init__(self):
        super().__init__("wikipedia_search", "Wikipedia Search")
        # Initialize the LLM verification node
        self.verification_node = None

    def set_verification_node(self, verification_node):
        """Set the LLM verification node for relevance checking"""
        self.verification_node = verification_node

    async def _verify_search_title(self, title: str, location: str, interest: str) -> Dict[str, Any]:
        """Use LLM to verify if a Wikipedia search title is relevant before fetching details"""
        if not self.verification_node:
            # If no verification node, assume relevant
            logger.warning("No verification node available, skipping Wikipedia title verification")
            return {
                "location_relevance": "uncertain",
                "field_relevance": "uncertain",
                "overall_classification": "UNCERTAIN",
                "confidence": "low",
                "recommendation": "include",
                "reason": "No verification node available"
            }

        try:
            verification_result = await self.verification_node.execute({
                "title": title,
                "location": location,
                "interest": interest
            })

            verification_data = json.loads(verification_result.get("text", "{}"))
            return verification_data.get("wikipedia_verification", {
                "location_relevance": "uncertain",
                "field_relevance": "uncertain",
                "overall_classification": "UNCERTAIN",
                "confidence": "low",
                "recommendation": "include",
                "reason": "Failed to parse verification result"
            })

        except Exception as e:
            logger.error(f"Error during Wikipedia title verification: {e}")
            return {
                "location_relevance": "uncertain",
                "field_relevance": "uncertain",
                "overall_classification": "UNCERTAIN",
                "confidence": "low",
                "recommendation": "include",
                "reason": f"Verification error: {str(e)}"
            }

    async def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Search Wikipedia using the wikipedia package and return all relevant results"""

        # Try to import wikipedia package
        try:
            import wikipedia
        except ImportError:
            logger.warning("Wikipedia package not available, using mock data")
            return {
                "results": [
                    {"text": "Mock Wikipedia content", "title": "Mock Title", "url": "https://en.wikipedia.org/wiki/Mock"}
                ],
                "text": "Mock Wikipedia content",  # Keep backward compatibility
                "title": "Mock Title",
                "url": "https://en.wikipedia.org/wiki/Mock"
            }

        # Set language
        language = WorkflowConfig.SEARCH_CONFIG["wikipedia"]["language"]
        wikipedia.set_lang(language)

        all_results = []

        # Try multiple search strategies
        search_queries = [
            f"{inputs['location']} {inputs['interest']}",  # Combined query
        ]

        for query in search_queries:
            try:
                logger.info(f"Trying Wikipedia search with query: '{query}'")

                # Try search for this query
                max_results = WorkflowConfig.SEARCH_CONFIG["wikipedia"].get("max_results", 10)
                if WorkflowConfig.TEST_MODE:
                    max_results = WorkflowConfig.TEST_CONFIG["max_results"]

                search_results = wikipedia.search(query, results=max_results)
                search_results = list(set(search_results))
                logger.info(f"Found {len(search_results)} search results for query: {query} (limited to {max_results})")
                if search_results:
                    # Apply LLM verification before fetching page details
                    if self.verification_node:
                        logger.info(f"Pre-filtering {len(search_results)} search titles with LLM verification")
                        verified_titles = []

                        for i, result_title in enumerate(search_results):
                            logger.info(f"[{i+1}/{len(search_results)}] Verifying title: {result_title}")

                            verification = await self._verify_search_title(
                                result_title,
                                inputs.get('location', ''),
                                inputs.get('interest', '')
                            )

                            classification = verification.get('overall_classification', 'UNCERTAIN')
                            location_relevance = verification.get('location_relevance', 'uncertain')
                            field_relevance = verification.get('field_relevance', 'uncertain')

                            # Only include if related to BOTH location AND field
                            if (classification == 'RELEVANT' and
                                location_relevance == 'relevant' and
                                field_relevance == 'relevant'):
                                verified_titles.append(result_title)
                                logger.info(f"✓ Title approved: {result_title} (BOTH location and field relevant)")
                            else:
                                logger.info(f"✗ Title filtered out: {result_title} (classification: {classification}, location: {location_relevance}, field: {field_relevance})")

                        logger.info(f"After title verification: {len(verified_titles)}/{len(search_results)} titles approved")
                        search_results = verified_titles

                    # Now fetch page details only for verified titles
                    for i, result_title in enumerate(search_results):
                        try:
                            logger.info(f"[{i+1}/{len(search_results)}] Fetching page details for: {result_title}")
                            page = wikipedia.page(result_title)
                            result = {
                                "title": page.title,
                                "text": page.summary,
                                "content": page.content,
                                "url": page.url,
                                "query": query,
                                "type": "search",
                                "search_title": result_title
                            }
                            all_results.append(result)
                            logger.debug(f"Successfully got page from search result: {page.title}")
                        except Exception as page_e:
                            logger.debug(f"Failed to get page for search result '{result_title}': {page_e}")
                            continue
                else:
                    logger.debug(f"No search results found for query: {query}")

            except Exception as e:
                logger.error(f"Wikipedia search error for query '{query}': {e}")
                import traceback
                logger.debug(traceback.format_exc())

        # Remove duplicates based on title
        unique_results = []
        seen_urls = set()
        for result in all_results:
            if result["url"] not in seen_urls:
                unique_results.append(result)
                seen_urls.add(result["url"])

        if unique_results:
            logger.info(f"Found {len(unique_results)} unique Wikipedia results (pre-filtered by LLM)")
            return unique_results
        else:
            logger.warning("No Wikipedia results found for any search query")
            return []


class EnhancedSerpSearchNode(BaseNode):
    """Enhanced search using SERP API"""

    def __init__(self, node_id: str, title: str):
        super().__init__(node_id, title)
        self.api_key = WorkflowConfig.SERP_API_KEY
        self.base_url = "https://serpapi.com/search"
    
    async def execute(self, inputs: Dict[str, Any], search_num: int = 10) -> Dict[str, Any]:
        """Perform search using SERP API"""
        if not self.api_key:
            logger.warning("SERP API key not configured, using mock data")
            return self._mock_search_results(inputs)

        # Try to import SERP API when needed
        try:
            from serpapi import GoogleSearch
        except ImportError:
            logger.warning("SERP API library not available, using mock data")
            return self._mock_search_results(inputs)

        query = inputs.get("query", f"{inputs.get('interest', '')} {inputs.get('location', '')}")

        try:
            logger.info(f"SERP API query: {query}")
            # Use SERP API library for synchronous search
            search = GoogleSearch({
                "q": query,
                "num": max(search_num, WorkflowConfig.SEARCH_CONFIG["serp"]["num_results"]),
                "gl": WorkflowConfig.GOOGLE_COUNTRY,
                "hl": WorkflowConfig.GOOGLE_LANGUAGE,
                "api_key": self.api_key
            })

            data = search.get_dict()
            if "error" in data:
                logger.error(f"SERP API error: {data['error']}")
                return self._mock_search_results(inputs)

            organic_results = data.get("organic_results", [])

            results = []
            for item in organic_results:
                results.append({
                    "title": item.get("title", ""),
                    "snippet": item.get("snippet", ""),
                    "link": item.get("link", ""),
                    "displayLink": item.get("displayed_link", "")
                })
            logger.info(f"SERP API response: {len(results)} results")

            return {"json": results}

        except Exception as e:
            logger.error(f"SERP API error: {e}")
            return self._mock_search_results(inputs)
    
    def _mock_search_results(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Return mock search results when API is not available"""
        query = inputs.get("query", "")
        return {
            "json": [
                {
                    "title": f"Sample result for {query}",
                    "snippet": f"This is a sample snippet for {query} search results.",
                    "link": "https://example.com/sample1",
                    "displayLink": "example.com"
                },
                {
                    "title": f"Another result for {query}",
                    "snippet": f"Another sample snippet related to {query}.",
                    "link": "https://example.org/sample2", 
                    "displayLink": "example.org"
                }
            ]
        }


class EnhancedSerperSearchNode(BaseNode):
    """Enhanced search using Serper API (serper.dev)"""

    def __init__(self, node_id: str, title: str):
        super().__init__(node_id, title)
        self.api_key = WorkflowConfig.SERPER_API_KEY
        self.base_url = WorkflowConfig.SEARCH_CONFIG["serper"]["base_url"]

    async def execute(self, inputs: Dict[str, Any], search_num: int = 10) -> Dict[str, Any]:
        """Perform search using Serper API"""
        if not self.api_key:
            logger.warning("Serper API key not configured, using mock data")
            return self._mock_search_results(inputs)

        query = inputs.get("query", f"{inputs.get('interest', '')} {inputs.get('location', '')}")

        try:
            logger.info(f"Serper API query: {query}")

            # Prepare request payload
            payload = {
                "q": query,
                "num": max(search_num, WorkflowConfig.SEARCH_CONFIG["serper"]["num_results"]),
                "gl": WorkflowConfig.GOOGLE_COUNTRY,
                "hl": WorkflowConfig.GOOGLE_LANGUAGE
            }

            headers = {
                'X-API-KEY': self.api_key,
                'Content-Type': 'application/json'
            }

            # Make async HTTP request to Serper API
            async with aiohttp.ClientSession() as session:
                async with session.post(self.base_url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()

                        # Extract organic results from Serper API response
                        organic_results = data.get("organic", [])

                        results = []
                        for item in organic_results:
                            results.append({
                                "title": item.get("title", ""),
                                "snippet": item.get("snippet", ""),
                                "link": item.get("link", ""),
                                "displayLink": item.get("displayedLink", "")
                            })

                        logger.info(f"Serper API response: {len(results)} results")
                        return {"json": results}

                    else:
                        logger.error(f"Serper API error: HTTP {response.status}")
                        error_text = await response.text()
                        logger.error(f"Error details: {error_text}")
                        return self._mock_search_results(inputs)

        except Exception as e:
            logger.error(f"Serper API error: {e}")
            return self._mock_search_results(inputs)

    def _mock_search_results(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Return mock search results when API is not available"""
        query = inputs.get("query", "")
        return {
            "json": [
                {
                    "title": f"Sample Serper result for {query}",
                    "snippet": f"This is a sample snippet for {query} search results from Serper API.",
                    "link": "https://example.com/serper1",
                    "displayLink": "example.com"
                },
                {
                    "title": f"Another Serper result for {query}",
                    "snippet": f"Another sample snippet related to {query} from Serper API.",
                    "link": "https://example.org/serper2",
                    "displayLink": "example.org"
                }
            ]
        }


class EnhancedLLMNode(BaseNode):
    """Enhanced LLM node with multiple provider support"""
    
    def __init__(self, node_id: str, title: str, prompt_type: str):
        super().__init__(node_id, title)
        self.prompt_type = prompt_type
        self.model_config = WorkflowConfig.get_model_config(prompt_type)
        self.time_out = WorkflowConfig.NODE_CONFIG["timeout"]
    
    async def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Execute LLM inference with real API calls"""
        prompt = self._format_prompt(inputs)
        
        if self.model_config["provider"] == "ollama":
            return await self._call_ollama(prompt)
        elif self.model_config["provider"] == "openai":
            return await self._call_openai(prompt)
        else:
            return self._mock_llm_response(inputs)
    
    def _format_prompt(self, inputs: Dict[str, Any]) -> str:
        """Format the prompt template with input variables"""
        template = PROMPTS.get(self.prompt_type, "")
        return template.format(**inputs)
    
    async def _call_ollama(self, prompt: str) -> Dict[str, Any]:
        """Call Ollama API"""
        url = f"{WorkflowConfig.OLLAMA_BASE_URL}/api/generate"
        
        payload = {
            "model": self.model_config["name"],
            "prompt": prompt,
            "format": self.model_config.get("format", "json"),
            "stream": False
        }
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(self.time_out)) as session:
            try:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {"text": data.get("response", "{}")}
                    else:
                        logger.error(f"Ollama API error: {response.status}")
            except Exception as e:
                logger.error(f"Ollama API error: {e}")
        
        return self._mock_llm_response({})
    
    async def _call_openai(self, prompt: str) -> Dict[str, Any]:
        """Call OpenAI API"""
        if not WorkflowConfig.OPENAI_API_KEY:
            logger.warning("OpenAI API key not configured")
            return self._mock_llm_response({})
        
        url = "https://api.openai.com/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {WorkflowConfig.OPENAI_API_KEY}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "gpt-3.5-turbo",
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7
        }
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(self.time_out)) as session:
            try:
                async with session.post(url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        content = data["choices"][0]["message"]["content"]
                        return {"text": content}
                    else:
                        logger.error(f"OpenAI API error: {response.status}")
            except Exception as e:
                logger.error(f"OpenAI API error: {e}")
        
        return self._mock_llm_response({})
    
    def _mock_llm_response(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Generate mock LLM response"""
        if self.prompt_type == "entity_extraction":
            return {"text": json.dumps({"entities": ["MockEntity1", "MockEntity2", "MockEntity3"]})}
        elif self.prompt_type == "domain_extraction":
            entity = inputs.get("item", "MockEntity")
            return {"text": json.dumps({"entity": entity, "link": "https://mock-example.com"})}
        elif self.prompt_type == "knowledge_extraction":
            location = inputs.get("location", "Unknown Location")
            interest = inputs.get("interest", "Unknown Interest")
            return {"text": json.dumps({
                "entities": [
                    f"Leading {interest} company in {location}",
                    f"Regional {interest} association in {location}",
                    f"Major {interest} research institute in {location}",
                    f"Government {interest} department in {location}",
                    f"Top {interest} startup in {location}"
                ]
            })}
        elif self.prompt_type == "subdomain_verification":
            # Mock subdomain verification - simulate filtering out public domains
            entity = inputs.get("entity", "MockEntity")
            return {"text": json.dumps({
                "verified_subdomains": [
                    {"domain": f"{entity.lower().replace(' ', '')}.com", "classification": "ORGANIZATIONAL", "confidence": "high", "reason": "Official company website"},
                    {"domain": f"api.{entity.lower().replace(' ', '')}.com", "classification": "ORGANIZATIONAL", "confidence": "high", "reason": "Company API subdomain"},
                    {"domain": f"support.{entity.lower().replace(' ', '')}.com", "classification": "ORGANIZATIONAL", "confidence": "medium", "reason": "Company support portal"}
                ]
            })}
        elif self.prompt_type == "entity_location_verification":
            # Mock entity location verification
            entity = inputs.get("entity", "MockEntity")
            location = inputs.get("location", "Unknown")
            interest = inputs.get("interest", "Unknown")
            return {"text": json.dumps({
                "entity_verification": {
                    "entity": entity,
                    "location_relevance": "relevant",
                    "field_relevance": "relevant",
                    "overall_classification": "RELEVANT",
                    "confidence": "high",
                    "location_evidence": f"Entity operates in {location}",
                    "field_evidence": f"Entity belongs to {interest} industry",
                    "recommendation": "include"
                }
            })}
        elif self.prompt_type == "wikipedia_relevance_verification":
            # Mock Wikipedia relevance verification - simulate strict both-criteria filtering
            title = inputs.get("title", "Mock Wikipedia Article")
            location = inputs.get("location", "Unknown")
            interest = inputs.get("interest", "Unknown")

            # Simulate that only some articles meet both criteria
            import hashlib
            title_hash = int(hashlib.md5(title.encode()).hexdigest(), 16)

            # Simulate 30% pass rate for strict both-criteria requirement
            if title_hash % 10 < 3:
                return {"text": json.dumps({
                    "wikipedia_verification": {
                        "title": title,
                        "location_relevance": "relevant",
                        "field_relevance": "relevant",
                        "overall_classification": "RELEVANT",
                        "confidence": "high",
                        "location_evidence": f"Article specifically mentions {location}",
                        "field_evidence": f"Article directly covers {interest} topics",
                        "information_value": "high",
                        "recommendation": "include",
                        "reason": f"Article satisfies BOTH location ({location}) and field ({interest}) criteria"
                    }
                })}
            else:
                # Simulate various rejection reasons
                rejection_type = title_hash % 4
                if rejection_type == 0:
                    return {"text": json.dumps({
                        "wikipedia_verification": {
                            "title": title,
                            "location_relevance": "relevant",
                            "field_relevance": "not_relevant",
                            "overall_classification": "LOCATION_RELEVANT",
                            "confidence": "medium",
                            "location_evidence": f"Article mentions {location}",
                            "field_evidence": f"Article does not cover {interest} topics",
                            "information_value": "low",
                            "recommendation": "exclude",
                            "reason": f"Only location-relevant, not related to {interest}"
                        }
                    })}
                elif rejection_type == 1:
                    return {"text": json.dumps({
                        "wikipedia_verification": {
                            "title": title,
                            "location_relevance": "not_relevant",
                            "field_relevance": "relevant",
                            "overall_classification": "FIELD_RELEVANT",
                            "confidence": "medium",
                            "location_evidence": f"Article does not mention {location}",
                            "field_evidence": f"Article covers {interest} topics",
                            "information_value": "low",
                            "recommendation": "exclude",
                            "reason": f"Only field-relevant, not related to {location}"
                        }
                    })}
                else:
                    return {"text": json.dumps({
                        "wikipedia_verification": {
                            "title": title,
                            "location_relevance": "not_relevant",
                            "field_relevance": "not_relevant",
                            "overall_classification": "NOT_RELEVANT",
                            "confidence": "high",
                            "location_evidence": f"Article does not mention {location}",
                            "field_evidence": f"Article does not cover {interest} topics",
                            "information_value": "low",
                            "recommendation": "exclude",
                            "reason": "Article is not relevant to either location or field"
                        }
                    })}

        return {"text": "{}"}


class EnhancedTargetExploitWorkflow:
    """Enhanced workflow with real API integrations"""
    
    def __init__(self):
        self.config_valid = WorkflowConfig.validate_config()
        self._setup_nodes()
    
    def _setup_nodes(self):
        """Setup enhanced nodes with real API integrations"""
        self.wikipedia_node = EnhancedWikipediaSearchNode()

        # Setup search nodes based on available API keys
        # Prefer Serper API if available, fallback to SERP API
        if WorkflowConfig.SERPER_API_KEY:
            logger.info("Using Serper API for search functionality")
            self.search_node = EnhancedSerperSearchNode("serper_search", "Serper Search")
            self.search_2_node = EnhancedSerperSearchNode("serper_search_2", "Serper Search 2")
        elif WorkflowConfig.SERP_API_KEY:
            logger.info("Using SERP API for search functionality")
            self.search_node = EnhancedSerpSearchNode("serp_search", "SERP Search")
            self.search_2_node = EnhancedSerpSearchNode("serp_search_2", "SERP Search 2")
        else:
            logger.error("No search API keys configured, using SERP API with mock data")
            self.search_node = EnhancedSerpSearchNode("serp_search", "SERP Search")
            self.search_2_node = EnhancedSerpSearchNode("serp_search_2", "SERP Search 2")

        self.entity_extraction_node = EnhancedLLMNode("entity_extraction", "Entity Extraction", "entity_extraction")
        self.domain_extraction_node = EnhancedLLMNode("domain_extraction", "Domain Extraction", "domain_extraction")
        self.subdomain_verification_node = EnhancedLLMNode("subdomain_verification", "Subdomain Verification", "subdomain_verification")
        self.entity_location_verification_node = EnhancedLLMNode("entity_location_verification", "Entity Location Verification", "entity_location_verification")
        self.wikipedia_verification_node = EnhancedLLMNode("wikipedia_verification", "Wikipedia Relevance Verification", "wikipedia_relevance_verification")

        # Connect the verification node to the Wikipedia node
        self.wikipedia_node.set_verification_node(self.wikipedia_verification_node)
    
    async def execute(self, workflow_input: WorkflowInput) -> List[EntityResult]:
        """Execute the enhanced workflow"""
        logger.info(f"Starting enhanced workflow for location: {workflow_input.location}, interest: {workflow_input.interest}")

        inputs = {
            "location": workflow_input.location,
            "interest": workflow_input.interest
        }

        # Step 1: Wikipedia search and entity extraction
        logger.info("Step 1: Wikipedia search and entity extraction")
        wiki_result = await self.wikipedia_node.execute(inputs)

        # Extract entities from Wikipedia results
        wikipedia_entities = await self._extract_entities_from_wikipedia(wiki_result, inputs)
        logger.info(f"Extracted {len(wikipedia_entities)} entities from Wikipedia: {wikipedia_entities}")

        # Step 2: SERP search and entity extraction
        logger.info("Step 2: SERP search and entity extraction")
        max_search_results = WorkflowConfig.ENTITY_EXTRACTION_CONFIG["max_search_results"]
        if WorkflowConfig.ENTITY_EXTRACTION_CONFIG["test_mode"]:
            max_search_results = WorkflowConfig.ENTITY_EXTRACTION_CONFIG["test_max_results"]

        search_result = await self.search_node.execute({
            **inputs,
            "query": f"{inputs['location']} {inputs['interest']}"
        }, search_num=max_search_results)

        # Extract entities from search results
        search_entities = await self._extract_entities_from_search_results(search_result.get("json", []), inputs)
        logger.info(f"Extracted {len(search_entities)} entities from search results: {search_entities}")

        # Step 3: Merge and deduplicate entities
        logger.info("Step 3: Merging and deduplicating entities")
        entities = self._merge_and_deduplicate_entities(wikipedia_entities, search_entities)
        logger.info(f"Final merged entity list ({len(entities)} entities): {entities}")

        # Step 4: Process each entity
        logger.info(f"Step 4: Processing {len(entities)} entities")
        results = []
        
        for i, entity in enumerate(entities):
            logger.info(f"[{i}/{len(entities)}] Processing entity: {entity}")

            # Search for specific entity
            entity_search = await self.search_2_node.execute({
                **inputs,
                "query": f"{entity}"
            }, search_num=50)

            entity_search_results = self._format_search_results(entity_search.get("json", []), simple=True)

            # Verify entity location and field relevance
            logger.info(f"Verifying location and field relevance for entity: {entity}")
            entity_verification = await self._verify_entity_location_and_field(
                entity,
                inputs['location'],
                inputs['interest'],
                entity_search_results[:min(20, len(entity_search_results))]
            )

            # Check if entity should be included based on verification
            if entity_verification.get("recommendation") == "exclude":
                logger.info(f"Excluding entity {entity} based on verification: {entity_verification.get('overall_classification')}")
                continue
            elif entity_verification.get("recommendation") == "needs_review":
                logger.warning(f"Entity {entity} needs manual review: {entity_verification.get('overall_classification')}")
                # Continue processing but flag for review

            # Extract all subdomains from search results with context
            search_results_list = entity_search.get("json", [])
            subdomains_with_context = self._extract_all_subdomains(search_results_list)

            # Use LLM to verify which subdomains actually belong to the organization
            logger.info(f"Verifying {len(subdomains_with_context)} subdomains for {entity} using LLM...")
            verified_subdomains = await self._verify_subdomains_with_llm(
                entity,
                subdomains_with_context
            )


            # Resolve IP addresses for verified subdomains
            subdomain_ips = []
            if verified_subdomains and WorkflowConfig.DNS_CONFIG.get("enabled", True):
                logger.info(f"Resolving IP addresses for {len(verified_subdomains)} verified subdomains...")
                subdomain_ips = await resolve_subdomains_ips(
                    verified_subdomains,
                    timeout=WorkflowConfig.DNS_CONFIG.get("timeout", 5)
                )

                # Log IP resolution summary
                successful_ips = len([info for info in subdomain_ips if info.ip_address])
                logger.info(f"Successfully resolved {successful_ips}/{len(verified_subdomains)} subdomain IP addresses")

            # Extract domain for entity (keep existing functionality)
            domain_result = await self.domain_extraction_node.execute({
                **inputs,
                "entity": entity,
                "search_results": entity_search_results
            })

            try:
                domain_data = json.loads(domain_result.get("text", "{}"))
                entity_name = domain_data.get("entity", entity)
                link = domain_data.get("link", "")
                domain = self._extract_domain(link) if link else ""

                results.append(EntityResult(
                    entity=entity_name,
                    link=link,
                    domain=domain,
                    subdomains=verified_subdomains,
                    subdomain_ips=subdomain_ips,
                    location_verification=entity_verification,
                    subdomain_location_verification=[]
                ))

                logger.info(f"Verified {len(verified_subdomains)} organizational subdomains for {entity}: {self._format_subdomains_for_display(verified_subdomains)}")

            except json.JSONDecodeError:
                logger.error(f"Failed to parse domain extraction result for {entity}")
                results.append(EntityResult(
                    entity=entity,
                    link="",
                    domain="",
                    subdomains=verified_subdomains,
                    subdomain_ips=subdomain_ips,
                    location_verification=entity_verification,
                    subdomain_location_verification=[]
                ))
        
        return results

    async def _verify_entity_location_and_field(self, entity: str, location: str, interest: str, search_results: str) -> Dict[str, Any]:
        """Verify if an entity is relevant to the specified location and field using LLM"""
        try:
            verification_result = await self.entity_location_verification_node.execute({
                "entity": entity,
                "location": location,
                "interest": interest,
                "search_results": search_results
            })

            verification_data = json.loads(verification_result.get("text", "{}"))
            return verification_data.get("entity_verification", {})

        except json.JSONDecodeError:
            logger.error(f"Failed to parse entity location verification result for {entity}")
            return {
                "entity": entity,
                "location_relevance": "uncertain",
                "field_relevance": "uncertain",
                "overall_classification": "INSUFFICIENT_DATA",
                "confidence": "low",
                "location_evidence": "Failed to parse verification result",
                "field_evidence": "Failed to parse verification result",
                "recommendation": "needs_review"
            }
        except Exception as e:
            logger.error(f"Error during entity location verification for {entity}: {e}")
            return {
                "entity": entity,
                "location_relevance": "uncertain",
                "field_relevance": "uncertain",
                "overall_classification": "INSUFFICIENT_DATA",
                "confidence": "low",
                "location_evidence": f"Verification error: {str(e)}",
                "field_evidence": f"Verification error: {str(e)}",
                "recommendation": "needs_review"
            }


    
    def _format_search_results(self, results: List[Dict[str, Any]], simple: bool=False) -> str:
        """Format search results for LLM consumption"""
        formatted = []
        for result in results:
            formatted.append(f"Title: {result.get('title', '')}")
            formatted.append(f"Link: {result.get('link', '')}")
            if not simple:
                formatted.append(f"Snippet: {result.get('snippet', '')}")
            formatted.append("---")
        formatted = "\n".join(formatted)
        logger.info(f"Successfully formatted {len(results)} search results to token {len(formatted)}")
        return formatted

    def _extract_domain(self, url: str) -> str:
        """Extract domain from URL"""
        try:
            parsed = urlparse(url)
            domain = parsed.netloc
            return domain.split('www.')[-1] if domain.startswith('www.') else domain
        except Exception:
            return ""

    def _extract_all_subdomains(self, search_results: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """Extract all unique subdomains from search results with context"""
        subdomains_with_context = []
        seen_domains = set()

        for result in search_results:
            link = result.get("link", "")
            title = result.get("title", "")
            snippet = result.get("snippet", "")

            if link:
                try:
                    parsed = urlparse(link)
                    domain = parsed.netloc
                    if domain:
                        # Remove www. prefix if present
                        if domain.startswith('www.'):
                            domain = domain[4:]

                        # Add the full domain/subdomain with context
                        if domain and '.' in domain and domain not in seen_domains:
                            subdomains_with_context.append({
                                "domain": domain,
                                "title": title,
                                "snippet": snippet,
                                "url": link
                            })
                            seen_domains.add(domain)

                            # Also extract the root domain if different
                            parts = domain.split('.')
                            if len(parts) >= 2:
                                root_domain = '.'.join(parts[-2:])
                                if root_domain != domain and root_domain not in seen_domains:
                                    subdomains_with_context.append({
                                        "domain": root_domain,
                                        "title": f"Root domain of {domain}",
                                        "snippet": snippet,
                                        "url": link
                                    })
                                    seen_domains.add(root_domain)

                except Exception as e:
                    logger.debug(f"Failed to parse URL {link}: {e}")
                    continue

        return subdomains_with_context

    def _format_subdomains_for_display(self, subdomains: List[str]) -> str:
        """Format subdomains list for display"""
        if not subdomains:
            return ""
        return ", ".join(subdomains)

    async def _verify_subdomains_with_llm(self, entity: str, subdomains_with_context: List[Dict[str, str]]) -> List[str]:
        """Use LLM to verify which subdomains actually belong to the organization"""
        if not subdomains_with_context:
            return []

        # Format subdomain data for LLM analysis
        subdomains_data = []
        for item in subdomains_with_context:
            subdomains_data.append(
                f"Domain: {item['domain']}\n"
                f"Title: {item['title']}\n"
                f"Snippet: {item['snippet'][:200]}...\n"
                f"URL: {item['url']}\n"
                "---"
            )

        subdomains_text = "\n".join(subdomains_data)

        try:
            # Call LLM for subdomain verification
            verification_result = await self.subdomain_verification_node.execute({
                "entity": entity,
                "subdomains_data": subdomains_text
            })

            # Parse LLM response
            verification_data = json.loads(verification_result.get("text", "{}"))
            verified_subdomains = verification_data.get("verified_subdomains", [])

            # Extract only the verified domain names
            organizational_domains = []
            for subdomain_info in verified_subdomains:
                domain = subdomain_info.get("domain", "")
                classification = subdomain_info.get("classification", "")
                confidence = subdomain_info.get("confidence", "")
                reason = subdomain_info.get("reason", "")

                if domain and classification == "ORGANIZATIONAL":
                    organizational_domains.append(domain)
                    logger.info(f"Verified subdomain for {entity}: {domain} ({classification}, {confidence}) - {reason}")

            return sorted(organizational_domains)

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse subdomain verification result for {entity}: {e}")
            # Fallback: return all domains if LLM verification fails
            return sorted([item["domain"] for item in subdomains_with_context])
        except Exception as e:
            logger.error(f"Error during subdomain verification for {entity}: {e}")
            # Fallback: return all domains if verification fails
            return sorted([item["domain"] for item in subdomains_with_context])

    def _format_wikipedia_result(self, wiki_result: List[Dict[str, Any]]) -> str:
        """Format Wikipedia search results into readable text"""
        if not wiki_result:
            logger.warning("Empty Wikipedia search result")
            return "No Wikipedia results found."

        if not isinstance(wiki_result, list):
            logger.warning(f"Unexpected Wikipedia result format: {type(wiki_result)}")
            return str(wiki_result)

        try:
            formatted_sections = []
            formatted_sections.append("Wikipedia Search Results:")
            formatted_sections.append("=" * 50)

            for i, result in enumerate(wiki_result, 1):
                if not isinstance(result, dict):
                    logger.warning(f"Unexpected Wikipedia result item format: {type(result)}")
                    continue

                title = result.get("title", "Unknown Title")
                text = result.get("text", "")
                url = result.get("url", "")
                query = result.get("query", "")
                result_type = result.get("type", "unknown")

                # Format each result
                formatted_sections.append(f"\n{i}. {title}")
                formatted_sections.append("-" * len(f"{i}. {title}"))

                # if url:
                #     formatted_sections.append(f"URL: {url}")

                # if query:
                #     formatted_sections.append(f"Search Query: {query}")

                if result_type:
                    formatted_sections.append(f"Result Type: {result_type}")

                # Add summary text (truncated if too long)
                if text:
                    # Limit text to reasonable length for context
                    max_text_length = 200
                    if len(text) > max_text_length:
                        truncated_text = text[:max_text_length] + "..."
                        formatted_sections.append(f"Summary: {truncated_text}")
                    else:
                        formatted_sections.append(f"Summary: {text}")
                else:
                    formatted_sections.append("Summary: No summary available")

                # Add separator between results
                if i < len(wiki_result):
                    formatted_sections.append("")

            formatted_text = "\n".join(formatted_sections)
            logger.info(f"Successfully formatted {len(wiki_result)} Wikipedia results to token {len(formatted_text)}")
            return formatted_text

        except Exception as e:
            logger.error(f"Error formatting Wikipedia results: {e}")
            # Fallback: create basic formatted output
            try:
                fallback_sections = ["Wikipedia Search Results (Basic Format):"]
                for i, result in enumerate(wiki_result, 1):
                    if isinstance(result, dict):
                        title = result.get("title", f"Result {i}")
                        fallback_sections.append(f"{i}. {title}")
                    else:
                        fallback_sections.append(f"{i}. {str(result)}")
                formatted_text = "\n".join(fallback_sections)
                logger.info(f"Basic formatted {len(wiki_result)} Wikipedia results to token {len(formatted_text)}")
                return formatted_text
            except Exception as fallback_e:
                logger.error(f"Error in Wikipedia formatting fallback: {fallback_e}")
                return f"Wikipedia results (raw): {str(wiki_result)}"

    def _format_knowledge_extraction_result(self, knowledge_result: Dict[str, Any]) -> str:
        """Format LLM knowledge extraction results and handle JSON output exceptions"""
        raw_text = knowledge_result.get("text", "")

        if not raw_text:
            logger.warning("Empty knowledge extraction result")
            return "No knowledge extracted from LLM."

        try:
            # Try to parse as JSON first
            knowledge_data = json.loads(raw_text)
            entities = knowledge_data.get("entities", [])

            if entities:
                # Format as a readable list
                formatted_entities = []
                for i, entity in enumerate(entities, 1):
                    if isinstance(entity, str):
                        formatted_entities.append(f"{i}. {entity}")
                    elif isinstance(entity, dict):
                        # Handle more complex entity objects
                        name = entity.get("name", entity.get("entity", str(entity)))
                        formatted_entities.append(f"{i}. {name}")
                    else:
                        formatted_entities.append(f"{i}. {str(entity)}")

                formatted_text = "LLM Knowledge Extraction Results:\n" + "\n".join(formatted_entities)
                logger.info(f"Successfully formatted {len(entities)} knowledge entities to token {len(formatted_text)}")
                return formatted_text
            else:
                logger.warning("No entities found in knowledge extraction result")
                return "LLM Knowledge Extraction: No entities identified."

        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse knowledge extraction JSON: {e}")
            # Try to extract useful information from raw text
            formatted_text = self._extract_knowledge_from_raw_text(raw_text)
            return formatted_text
        except Exception as e:
            logger.error(f"Unexpected error formatting knowledge extraction result: {e}")
            # Fallback to raw text with basic formatting
            return f"LLM Knowledge Extraction (raw):\n{raw_text}"

    async def _extract_entities_from_wikipedia(self, wiki_result: List[Dict[str, Any]], inputs: Dict[str, Any]) -> List[str]:
        """Extract entities from Wikipedia search results using LLM"""
        if not wiki_result:
            logger.warning("No Wikipedia results to extract entities from")
            return []

        # Format Wikipedia results for entity extraction
        formatted_wikipedia = self._format_wikipedia_result(wiki_result)

        # Use LLM to extract entities from Wikipedia content
        try:
            entity_result = await self.entity_extraction_node.execute({
                **inputs,
                "search_results": "",  # No search results for Wikipedia-only extraction
                "llm_knowledge": "",   # No LLM knowledge for Wikipedia-only extraction
                "wikipedia_text": f"**Wikipedia Information:**\n{formatted_wikipedia}\n\n"
            })

            entities_data = json.loads(entity_result.get("text", "{}"))
            entities = entities_data.get("entities", [])
            logger.info(f"Successfully extracted {len(entities)} entities from Wikipedia")
            return entities

        except json.JSONDecodeError:
            logger.error("Failed to parse Wikipedia entity extraction result")
            return []
        except Exception as e:
            logger.error(f"Error during Wikipedia entity extraction: {e}")
            return []

    async def _extract_entities_from_search_results(self, search_results: List[Dict[str, Any]], inputs: Dict[str, Any]) -> List[str]:
        """Extract entities from search results by processing each result individually with LLM"""
        if not search_results:
            logger.warning("No search results to extract entities from")
            return []

        # Apply result limit based on configuration
        max_results = WorkflowConfig.ENTITY_EXTRACTION_CONFIG["max_search_results"]
        if WorkflowConfig.ENTITY_EXTRACTION_CONFIG["test_mode"]:
            max_results = WorkflowConfig.ENTITY_EXTRACTION_CONFIG["test_max_results"]

        limited_results = search_results[:max_results]
        logger.info(f"Processing {len(limited_results)} search results for entity extraction (limited from {len(search_results)})")

        all_entities = []

        # Process each search result individually
        for i, result in enumerate(limited_results):
            logger.info(f"[{i+1}/{len(limited_results)}] Extracting entities from search result: {result.get('title', 'Unknown')}")

            # Format single search result for entity extraction
            single_result_text = self._format_single_search_result(result)

            try:
                # Use LLM to extract entities from this single search result
                entity_result = await self.entity_extraction_node.execute({
                    **inputs,
                    "search_results": f"**Google Search Results:**\n{single_result_text}\n\n",
                    "llm_knowledge": "",   # No LLM knowledge for search-only extraction
                    "wikipedia_text": ""   # No Wikipedia for search-only extraction
                })

                entities_data = json.loads(entity_result.get("text", "{}"))
                entities = entities_data.get("entities", [])

                if entities:
                    all_entities.extend(entities)
                    logger.info(f"Extracted {len(entities)} entities from result '{result.get('title', 'Unknown')}': {entities}")
                else:
                    logger.debug(f"No entities found in result '{result.get('title', 'Unknown')}'")

            except json.JSONDecodeError:
                logger.error(f"Failed to parse entity extraction result for search result {i+1}")
                continue
            except Exception as e:
                logger.error(f"Error during entity extraction for search result {i+1}: {e}")
                continue

        logger.info(f"Total entities extracted from {len(limited_results)} search results: {len(all_entities)}")
        return all_entities

    def _format_single_search_result(self, result: Dict[str, Any]) -> str:
        """Format a single search result for LLM entity extraction"""
        formatted = []
        formatted.append(f"Title: {result.get('title', '')}")
        formatted.append(f"Link: {result.get('link', '')}")
        formatted.append(f"Snippet: {result.get('snippet', '')}")
        formatted.append("---")
        return "\n".join(formatted)

    def _merge_and_deduplicate_entities(self, wikipedia_entities: List[str], search_entities: List[str]) -> List[str]:
        """Merge and deduplicate entities from Wikipedia and search results"""
        # Combine all entities
        all_entities = wikipedia_entities + search_entities

        if not all_entities:
            logger.warning("No entities to merge")
            return []

        # Simple deduplication by converting to set and back to list
        # This handles exact matches
        unique_entities = list(set(all_entities))

        # More sophisticated deduplication could be added here
        # For example, handling similar names, abbreviations, etc.
        deduplicated_entities = self._advanced_deduplication(unique_entities)

        logger.info(f"Entity deduplication: {len(all_entities)} -> {len(unique_entities)} -> {len(deduplicated_entities)}")
        return deduplicated_entities

    def _advanced_deduplication(self, entities: List[str]) -> List[str]:
        """Perform advanced deduplication to handle similar entity names"""
        if not entities:
            return []

        # For now, just return the simple deduplicated list
        # Future improvements could include:
        # - Fuzzy string matching
        # - Handling abbreviations and full names
        # - Removing very similar entities
        # - LLM-based similarity detection

        # Sort entities for consistent output
        return sorted(entities)

    def _extract_knowledge_from_raw_text(self, raw_text: str) -> str:
        """Extract knowledge entities from raw text when JSON parsing fails"""
        try:
            # Clean up the text
            cleaned_text = raw_text.strip()

            # Try to find JSON-like content within the text
            import re

            # Look for array-like patterns
            array_pattern = r'\[([^\]]+)\]'
            array_matches = re.findall(array_pattern, cleaned_text)

            if array_matches:
                # Try to extract entities from array-like content
                entities = []
                for match in array_matches:
                    # Split by commas and clean up
                    items = [item.strip().strip('"\'') for item in match.split(',')]
                    entities.extend([item for item in items if item and len(item) > 2])

                if entities:
                    formatted_entities = [f"{i}. {entity}" for i, entity in enumerate(entities, 1)]
                    return "LLM Knowledge Extraction (parsed from text):\n" + "\n".join(formatted_entities)

            # Look for numbered or bulleted lists
            list_pattern = r'(?:^|\n)\s*(?:\d+\.|\-|\*)\s*([^\n]+)'
            list_matches = re.findall(list_pattern, cleaned_text, re.MULTILINE)

            if list_matches:
                entities = [match.strip() for match in list_matches if match.strip()]
                if entities:
                    formatted_entities = [f"{i}. {entity}" for i, entity in enumerate(entities, 1)]
                    return "LLM Knowledge Extraction (from list format):\n" + "\n".join(formatted_entities)

            # If no structured content found, return formatted raw text
            return f"LLM Knowledge Extraction (unstructured):\n{cleaned_text}"

        except Exception as e:
            logger.error(f"Error extracting knowledge from raw text: {e}")
            return f"LLM Knowledge Extraction (raw):\n{raw_text}"

    def export_results_to_csv(self, results: List[EntityResult], workflow_input: WorkflowInput, filename: str = None) -> str:
        """Export workflow results to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_location = workflow_input.location.replace(" ", "_").replace("/", "_")
            safe_interest = workflow_input.interest.replace(" ", "_").replace("/", "_")
            filename = f"osint_results_{safe_location}_{safe_interest}_{timestamp}.csv"

        # Ensure the filename has .csv extension
        if not filename.endswith('.csv'):
            filename += '.csv'

        # Create output directory if it doesn't exist
        output_dir = "output"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        filepath = os.path.join(output_dir, filename)

        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'entity',
                    'primary_link',
                    'primary_domain',
                    'verified_subdomains_count',
                    'verified_subdomains',
                    'resolved_ips_count',
                    'resolved_ips',
                    'location',
                    'interest',
                    'timestamp',
                    'location_verification_status',
                    'location_verification_confidence',
                    'field_verification_status',
                    'overall_classification',
                    'location_evidence',
                    'field_evidence',
                    'recommendation'
                ]

                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                timestamp = datetime.now().isoformat()

                for result in results:
                    # Format subdomains as semicolon-separated string
                    subdomains_str = "; ".join(result.subdomains) if result.subdomains else ""
                    subdomain_count = len(result.subdomains) if result.subdomains else 0

                    # Format resolved IPs
                    resolved_ips = []
                    if result.subdomain_ips:
                        for ip_info in result.subdomain_ips:
                            if ip_info.ip_address:
                                resolved_ips.append(f"{ip_info.domain}:{ip_info.ip_address}")

                    resolved_ips_str = "; ".join(resolved_ips)
                    resolved_ips_count = len(resolved_ips)

                    # Extract verification data
                    location_verification = result.location_verification or {}
                    location_status = location_verification.get('location_relevance', 'unknown')
                    field_status = location_verification.get('field_relevance', 'unknown')
                    confidence = location_verification.get('confidence', 'unknown')
                    classification = location_verification.get('overall_classification', 'UNKNOWN')
                    location_evidence = location_verification.get('location_evidence', '')
                    field_evidence = location_verification.get('field_evidence', '')
                    recommendation = location_verification.get('recommendation', 'unknown')



                    writer.writerow({
                        'entity': result.entity or "",
                        'primary_link': result.link or "",
                        'primary_domain': result.domain or "",
                        'verified_subdomains_count': subdomain_count,
                        'verified_subdomains': subdomains_str,
                        'resolved_ips_count': resolved_ips_count,
                        'resolved_ips': resolved_ips_str,
                        'location': workflow_input.location,
                        'interest': workflow_input.interest,
                        'timestamp': timestamp,
                        'location_verification_status': location_status,
                        'location_verification_confidence': confidence,
                        'field_verification_status': field_status,
                        'overall_classification': classification,
                        'location_evidence': location_evidence,
                        'field_evidence': field_evidence,
                        'recommendation': recommendation
                    })

            logger.info(f"Results exported to CSV: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Failed to export results to CSV: {e}")
            return ""

    def load_entities_from_csv(self, filepath: str) -> List[EntityResult]:
        """Load previously saved entities from CSV file"""
        if not os.path.exists(filepath):
            logger.error(f"CSV file not found: {filepath}")
            return []

        try:
            entities = []
            with open(filepath, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)

                for row in reader:
                    # Parse subdomains
                    subdomains_str = row.get('verified_subdomains', '')
                    subdomains = [s.strip() for s in subdomains_str.split(';') if s.strip()] if subdomains_str else []

                    # Parse resolved IPs
                    resolved_ips_str = row.get('resolved_ips', '')
                    subdomain_ips = []
                    if resolved_ips_str:
                        for ip_entry in resolved_ips_str.split(';'):
                            if ':' in ip_entry:
                                domain, ip = ip_entry.strip().split(':', 1)
                                subdomain_ips.append(SubdomainInfo(domain=domain, ip_address=ip))

                    # Parse location verification
                    location_verification = None
                    if row.get('overall_classification'):
                        location_verification = {
                            'entity': row.get('entity', ''),
                            'location_relevance': row.get('location_verification_status', 'unknown'),
                            'field_relevance': row.get('field_verification_status', 'unknown'),
                            'overall_classification': row.get('overall_classification', 'UNKNOWN'),
                            'confidence': row.get('location_verification_confidence', 'unknown'),
                            'location_evidence': row.get('location_evidence', ''),
                            'field_evidence': row.get('field_evidence', ''),
                            'recommendation': row.get('recommendation', 'unknown')
                        }



                    entity_result = EntityResult(
                        entity=row.get('entity', ''),
                        link=row.get('primary_link', ''),
                        domain=row.get('primary_domain', ''),
                        subdomains=subdomains,
                        subdomain_ips=subdomain_ips,
                        location_verification=location_verification,
                        subdomain_location_verification=[]
                    )

                    entities.append(entity_result)

            logger.info(f"Loaded {len(entities)} entities from CSV: {filepath}")
            return entities

        except Exception as e:
            logger.error(f"Failed to load entities from CSV: {e}")
            return []

    def merge_entities_with_existing(self, new_entities: List[EntityResult], existing_entities: List[EntityResult]) -> List[EntityResult]:
        """Merge new entities with existing ones, avoiding duplicates"""
        # Create a set of existing entity names for quick lookup
        existing_names = {entity.entity.lower() if entity.entity else '' for entity in existing_entities}

        merged_entities = existing_entities.copy()
        new_count = 0

        for new_entity in new_entities:
            entity_name = new_entity.entity.lower() if new_entity.entity else ''
            if entity_name and entity_name not in existing_names:
                merged_entities.append(new_entity)
                existing_names.add(entity_name)
                new_count += 1
            else:
                logger.debug(f"Skipping duplicate entity: {new_entity.entity}")

        logger.info(f"Merged {new_count} new entities with {len(existing_entities)} existing entities")
        return merged_entities

    def save_entities_to_persistent_storage(self, entities: List[EntityResult], workflow_input: WorkflowInput) -> str:
        """Save entities to a persistent storage file that can be loaded later"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_location = workflow_input.location.replace(" ", "_").replace("/", "_")
        safe_interest = workflow_input.interest.replace(" ", "_").replace("/", "_")
        filename = f"persistent_entities_{safe_location}_{safe_interest}_{timestamp}.csv"

        # Create persistent storage directory
        storage_dir = "persistent_storage"
        if not os.path.exists(storage_dir):
            os.makedirs(storage_dir)

        filepath = os.path.join(storage_dir, filename)

        # Use the existing CSV export method but save to persistent storage
        temp_filepath = self.export_results_to_csv(entities, workflow_input, filename)

        if temp_filepath:
            # Move from output to persistent storage
            import shutil
            try:
                shutil.move(temp_filepath, filepath)
                logger.info(f"Entities saved to persistent storage: {filepath}")
                return filepath
            except Exception as e:
                logger.error(f"Failed to move file to persistent storage: {e}")
                return temp_filepath

        return ""

    def export_detailed_results_to_csv(self, results: List[EntityResult], workflow_input: WorkflowInput, filename: str = None) -> str:
        """Export detailed workflow results to CSV file with one row per subdomain"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_location = workflow_input.location.replace(" ", "_").replace("/", "_")
            safe_interest = workflow_input.interest.replace(" ", "_").replace("/", "_")
            filename = f"osint_detailed_results_{safe_location}_{safe_interest}_{timestamp}.csv"

        # Ensure the filename has .csv extension
        if not filename.endswith('.csv'):
            filename += '.csv'

        # Create output directory if it doesn't exist
        output_dir = "output"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        filepath = os.path.join(output_dir, filename)

        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'entity',
                    'primary_link',
                    'primary_domain',
                    'subdomain',
                    'subdomain_type',
                    'ip_address',
                    'dns_status',
                    'location',
                    'interest',
                    'timestamp'
                ]

                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                timestamp = datetime.now().isoformat()

                for result in results:
                    if result.subdomains:
                        # Create a mapping of subdomain to IP info for quick lookup
                        ip_info_map = {}
                        if result.subdomain_ips:
                            for ip_info in result.subdomain_ips:
                                ip_info_map[ip_info.domain] = ip_info

                        # Create one row per subdomain
                        for subdomain in result.subdomains:
                            # Determine subdomain type
                            subdomain_type = "primary" if subdomain == result.domain else "verified"

                            # Get IP information for this subdomain
                            ip_info = ip_info_map.get(subdomain)
                            ip_address = ip_info.ip_address if ip_info else ""
                            dns_status = "resolved" if ip_address else (ip_info.dns_error if ip_info else "not_resolved")

                            writer.writerow({
                                'entity': result.entity or "",
                                'primary_link': result.link or "",
                                'primary_domain': result.domain or "",
                                'subdomain': subdomain,
                                'subdomain_type': subdomain_type,
                                'ip_address': ip_address,
                                'dns_status': dns_status,
                                'location': workflow_input.location,
                                'interest': workflow_input.interest,
                                'timestamp': timestamp
                            })
                    else:
                        # Create one row for entity without subdomains
                        writer.writerow({
                            'entity': result.entity or "",
                            'primary_link': result.link or "",
                            'primary_domain': result.domain or "",
                            'subdomain': result.domain or "",
                            'subdomain_type': "primary",
                            'ip_address': "",
                            'dns_status': "not_resolved",
                            'location': workflow_input.location,
                            'interest': workflow_input.interest,
                            'timestamp': timestamp
                        })

            logger.info(f"Detailed results exported to CSV: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Failed to export detailed results to CSV: {e}")
            return ""


async def main():
    """Main execution function for enhanced workflow"""
    workflow = EnhancedTargetExploitWorkflow()
    
    # Example usage
    workflow_input = WorkflowInput(
        location="Tokyo",
        interest="cybersecurity companies"
    )
    
    results = await workflow.execute(workflow_input)

    print("\n=== Enhanced Workflow Results ===")
    for i, result in enumerate(results, 1):
        print(f"{i}. Entity: {result.entity}")
        print(f"   Link: {result.link}")
        print(f"   Domain: {result.domain}")
        if result.subdomains:
            print(f"   Subdomains ({len(result.subdomains)}): {', '.join(result.subdomains)}")
        else:
            print(f"   Subdomains: None found")
        print()

    # Export results to CSV files
    print("\n=== Exporting Results to CSV ===")

    # Export summary CSV
    summary_csv = workflow.export_results_to_csv(results, workflow_input)
    if summary_csv:
        print(f"✓ Summary results exported to: {summary_csv}")

    # Export detailed CSV
    detailed_csv = workflow.export_detailed_results_to_csv(results, workflow_input)
    if detailed_csv:
        print(f"✓ Detailed results exported to: {detailed_csv}")

    print(f"\n📊 Summary: Found {len(results)} entities with {sum(len(r.subdomains or []) for r in results)} total verified subdomains")


if __name__ == "__main__":
    asyncio.run(main())
